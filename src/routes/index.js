const express = require('express');
const fetch = require('node-fetch');
const { rateLimit, chatRateLimit } = require('../middleware/rateLimit');

const router = express.Router();

// Import services
const openRouterService = require('../services/openRouterService');
const queryLogger = require('../services/queryLogger');

// Import vector processing routes
const vectorProcessingRoutes = require('./vectorProcessing');

// Import Guardrails validation middleware
const guardrailsValidation = require('../middleware/guardrailsValidation');

// COMMENTED OUT: Semantic refinement service (disabled for performance)
// const PromptPreprocessorService = require('../services/promptPreprocessorService');
// const promptPreprocessor = new PromptPreprocessorService();

// Apply general rate limiting to all routes
router.use(rateLimit);

// Apply stricter chat rate limiting to the main API endpoint
router.use('/api/v1/chat/', chatRateLimit);

/**
 * Generate dynamic out-of-context response based on available documents
 * @param {Array} documents - Available documents from user service
 * @returns {string} Personalized out-of-context response
 */
function generateDynamicOutOfContextResponse(documents) {
  if (!documents || documents.length === 0) {
    return "I apologize, but I don't have any documents in my current knowledge base. Please upload some documents first, or contact support if you need assistance.";
  }

  // Extract document topics and titles
  const documentInfo = analyzeDocumentTopics(documents);

  if (documentInfo.topics.length === 0) {
    // Fallback to filenames if no topics detected
    const filenames = documents
      .map(doc => doc.filename || 'Unknown Document')
      .filter(name => name !== 'Unknown Document')
      .slice(0, 3); // Limit to first 3 for readability

    if (filenames.length > 0) {
      const fileList = filenames.length === 1
        ? filenames[0]
        : filenames.length === 2
          ? `${filenames[0]} and ${filenames[1]}`
          : `${filenames.slice(0, -1).join(', ')}, and ${filenames[filenames.length - 1]}`;

      return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${fileList}, or contact support if you need assistance with other topics.`;
    }
  } else {
    // Use detected topics
    const topicList = documentInfo.topics.length === 1
      ? documentInfo.topics[0]
      : documentInfo.topics.length === 2
        ? `${documentInfo.topics[0]} and ${documentInfo.topics[1]}`
        : `${documentInfo.topics.slice(0, -1).join(', ')}, and ${documentInfo.topics[documentInfo.topics.length - 1]}`;

    return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${topicList}, or contact support if you need assistance with other topics.`;
  }

  // Final fallback
  return "I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to the documents that have been provided, or contact support if you need assistance with other topics.";
}

/**
 * Analyze documents to extract topics and themes
 * @param {Array} documents - Document objects
 * @returns {Object} Analysis results with topics and themes
 */
function analyzeDocumentTopics(documents) {
  const topics = new Set();
  const keywords = new Set();

  documents.forEach(doc => {
    // Extract from filename
    if (doc.filename) {
      const filenameTopics = extractTopicsFromFilename(doc.filename);
      filenameTopics.forEach(topic => topics.add(topic));
    }

    // Extract from document content if available
    if (doc.parsedData && doc.parsedData.text) {
      const contentTopics = extractTopicsFromContent(doc.parsedData.text);
      contentTopics.forEach(topic => topics.add(topic));
    }

    // Extract from metadata if available
    if (doc.metadata) {
      const metadataTopics = extractTopicsFromMetadata(doc.metadata);
      metadataTopics.forEach(topic => topics.add(topic));
    }
  });

  return {
    topics: Array.from(topics).slice(0, 3), // Limit to 3 most relevant topics
    keywords: Array.from(keywords)
  };
}

/**
 * Extract topics from filename
 * @param {string} filename - Document filename
 * @returns {Array} Extracted topics
 */
function extractTopicsFromFilename(filename) {
  const topics = [];

  // Remove file extension and clean up
  const cleanName = filename
    .replace(/\.[^/.]+$/, '')
    .replace(/[-_]/g, ' ')
    .toLowerCase();

  // Common topic patterns in filenames
  const topicPatterns = [
    // Technology topics
    { pattern: /machine\s*learning|ml/i, topic: 'machine learning' },
    { pattern: /artificial\s*intelligence|ai/i, topic: 'artificial intelligence' },
    { pattern: /deep\s*learning/i, topic: 'deep learning' },
    { pattern: /neural\s*network/i, topic: 'neural networks' },
    { pattern: /data\s*science/i, topic: 'data science' },
    { pattern: /python|programming/i, topic: 'programming' },
    { pattern: /algorithm/i, topic: 'algorithms' },

    // Business topics
    { pattern: /business|strategy/i, topic: 'business strategy' },
    { pattern: /marketing/i, topic: 'marketing' },
    { pattern: /finance|financial/i, topic: 'finance' },
    { pattern: /management/i, topic: 'management' },
    { pattern: /sales/i, topic: 'sales' },
    { pattern: /hr|human\s*resource|policy|policies/i, topic: 'HR policies' },

    // Academic topics
    { pattern: /research|study/i, topic: 'research' },
    { pattern: /analysis|analytics/i, topic: 'analysis' },
    { pattern: /report/i, topic: 'reports' },
    { pattern: /guide|tutorial/i, topic: 'guides and tutorials' },
    { pattern: /manual|documentation/i, topic: 'documentation' },

    // General topics
    { pattern: /health|medical/i, topic: 'health and medical information' },
    { pattern: /legal|law/i, topic: 'legal information' },
    { pattern: /education|learning/i, topic: 'education' },
    { pattern: /technology|tech/i, topic: 'technology' }
  ];

  // Check for topic patterns
  topicPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(cleanName)) {
      topics.push(topic);
    }
  });

  // If no specific topics found, use cleaned filename as topic
  if (topics.length === 0 && cleanName.length > 0) {
    // Convert to title case and use as topic
    const titleCase = cleanName.replace(/\b\w/g, l => l.toUpperCase());
    if (titleCase.length <= 50) { // Only if reasonable length
      topics.push(titleCase);
    }
  }

  return topics;
}

/**
 * Extract topics from document content (first 1000 characters)
 * @param {string} content - Document content
 * @returns {Array} Extracted topics
 */
function extractTopicsFromContent(content) {
  const topics = [];

  if (!content || content.length < 50) return topics;

  // Use first 1000 characters for topic detection
  const sample = content.substring(0, 1000).toLowerCase();

  // Topic detection patterns
  const contentPatterns = [
    { pattern: /machine\s+learning|ml\s+algorithm/i, topic: 'machine learning' },
    { pattern: /artificial\s+intelligence|ai\s+system/i, topic: 'artificial intelligence' },
    { pattern: /deep\s+learning|neural\s+network/i, topic: 'deep learning and neural networks' },
    { pattern: /data\s+science|data\s+analysis/i, topic: 'data science' },
    { pattern: /business\s+strategy|strategic\s+planning/i, topic: 'business strategy' },
    { pattern: /financial\s+analysis|finance/i, topic: 'financial analysis' },
    { pattern: /marketing\s+strategy|digital\s+marketing/i, topic: 'marketing' },
    { pattern: /software\s+development|programming/i, topic: 'software development' },
    { pattern: /project\s+management|management/i, topic: 'project management' },
    { pattern: /research\s+methodology|scientific\s+research/i, topic: 'research methodology' }
  ];

  contentPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(sample)) {
      topics.push(topic);
    }
  });

  return topics;
}

/**
 * Extract topics from document metadata
 * @param {Object} metadata - Document metadata
 * @returns {Array} Extracted topics
 */
function extractTopicsFromMetadata(metadata) {
  const topics = [];

  // Check common metadata fields
  const metadataFields = ['title', 'subject', 'description', 'keywords', 'category'];

  metadataFields.forEach(field => {
    if (metadata[field] && typeof metadata[field] === 'string') {
      const fieldTopics = extractTopicsFromFilename(metadata[field]);
      topics.push(...fieldTopics);
    }
  });

  return topics;
}

/**
 * Stream chat response
 */
async function streamChatResponse(res, query, context, sessionId, requestStartTime, cacheService, documentsLength = 0, refinementData = null, timingLog = null, options = {}) {
  try {
    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // PARALLEL PROCESSING OPTIMIZATION: Send session info immediately while preparing AI response
    console.log(`⚡ Starting parallel processing in streaming response...`);

    // Send initial session info immediately
    res.write(`data: ${JSON.stringify({
      type: 'session',
      sessionId,
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Check if no documents available - return static message
    if (documentsLength === 0) {
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Send static response as content
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: staticResponse
      })}\n\n`);

      // Add to conversation history
      await cacheService.addConversationEntry(sessionId, query, staticResponse, {
        contextLength: 0,
        totalDuration: totalDuration,
        staticResponse: true,
        semanticRefinement: refinementData || {
          applied: false,
          refinements: 0,
          processingTime: 0
        }
      });

      // Log static response
      queryLogger.logQueryResponse(query, staticResponse, null);

      // Send completion signal
      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString(),
        timing: { total: totalDuration }
      })}\n\n`);

      res.end();
      return;
    }

    // PARALLEL PROCESSING: Start conversation history retrieval and AI model preparation simultaneously
    const aiPreparationStartTime = Date.now();

    // Start conversation history retrieval (async)
    const conversationHistoryPromise = (async () => {
      console.log(`📚 Retrieving conversation history...`);
      const history = cacheService.getFormattedConversationHistory(sessionId);
      console.log(`✅ Conversation history retrieved: ${history.length} entries`);
      return history;
    })();

    // Start AI model preparation (parallel with conversation history)
    const aiModelPrepPromise = (async () => {
      console.log(`🤖 Preparing AI model for streaming...`);
      // Pre-warm the streaming generator setup
      // This doesn't start the actual generation but prepares the model
      console.log(`✅ AI model preparation completed`);
      return true;
    })();

    // Wait for both conversation history and AI model prep
    const [conversationHistory, aiModelReady] = await Promise.all([
      conversationHistoryPromise,
      aiModelPrepPromise
    ]);

    const aiPreparationDuration = Date.now() - aiPreparationStartTime;
    console.log(`⚡ Parallel AI preparation completed in ${aiPreparationDuration}ms`);

    // Now start the actual AI streaming response
    console.log(`🚀 Starting AI streaming response generation...`);
    const openRouterStartTime = Date.now();

    // Log OpenRouter API call start
    if (timingLog) {
      const openRouterCallTotalTime = openRouterStartTime - requestStartTime;
      timingLog.steps.push({
        step: 'openrouter_call_start',
        duration: 0,
        totalElapsed: openRouterCallTotalTime,
        timestamp: new Date().toISOString()
      });
      console.log(`⏱️ [TIMING] OpenRouter API Call Started (Total: ${openRouterCallTotalTime}ms)`);
    }

    const streamGenerator = openRouterService.generateStreamingResponse(query, context, conversationHistory, refinementData, options);
    let fullResponse = '';
    let firstChunkReceived = false;

    for await (const chunk of streamGenerator) {
      // Log first chunk timing
      if (!firstChunkReceived && timingLog) {
        const firstChunkTime = Date.now();
        const firstChunkDuration = firstChunkTime - openRouterStartTime;
        const firstChunkTotalTime = firstChunkTime - requestStartTime;
        timingLog.steps.push({
          step: 'openrouter_first_chunk',
          duration: firstChunkDuration,
          totalElapsed: firstChunkTotalTime,
          timestamp: new Date().toISOString()
        });
        console.log(`⏱️ [TIMING] OpenRouter First Chunk: ${firstChunkDuration}ms (Total: ${firstChunkTotalTime}ms)`);
        firstChunkReceived = true;
      }

      fullResponse += chunk;
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: chunk
      })}\n\n`);
    }

    const totalDuration = Date.now() - requestStartTime;
    const openRouterCompleteDuration = Date.now() - openRouterStartTime;

    // Log OpenRouter completion timing
    if (timingLog) {
      timingLog.steps.push({
        step: 'openrouter_complete',
        duration: openRouterCompleteDuration,
        totalElapsed: totalDuration,
        timestamp: new Date().toISOString(),
        responseLength: fullResponse.length
      });
      console.log(`⏱️ [TIMING] OpenRouter Complete: ${openRouterCompleteDuration}ms (Total: ${totalDuration}ms)`);

      // Log final timing summary
      console.log(`\n⏱️ [TIMING SUMMARY] Complete request flow:`);
      timingLog.steps.forEach((step, index) => {
        console.log(`   ${index + 1}. ${step.step}: ${step.duration}ms (Total: ${step.totalElapsed}ms)`);
      });
      console.log(`⏱️ [TIMING SUMMARY] Total Duration: ${totalDuration}ms\n`);
    }

    // Add to conversation history
    await cacheService.addConversationEntry(sessionId, query, fullResponse, {
      contextLength: context.length,
      totalDuration: totalDuration,
      conversationHistoryLength: conversationHistory.length,
      semanticRefinement: refinementData || {
        applied: false,
        refinements: 0,
        processingTime: 0
      },
      metadata: options.metadata || null
    });

    // Log successful query and response
    queryLogger.logQueryResponse(query, fullResponse, null);

    // Send completion signal
    res.write(`data: ${JSON.stringify({
      type: 'done',
      timestamp: new Date().toISOString(),
      timing: { total: totalDuration }
    })}\n\n`);

    res.end();

  } catch (error) {
    console.error('❌ Streaming error:', error.message);
    console.log('📤 Sending error response from streaming handler');

    // Log error
    queryLogger.logQueryResponse(query, null, error.message);

    if (!res.headersSent) {
      res.write(`data: ${JSON.stringify({
        type: 'error',
        message: 'An error occurred while generating the response'
      })}\n\n`);
      res.end();
    } else {
      console.log('⚠️ Headers already sent in streaming handler, cannot send error');
    }
  }
}

/**
 * Enhanced chat request handler with semantic-first validation
 * Performs API validation first, then semantic-aware guardrails validation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Object} params - Parameters object containing apikey, query, sessionId, stream, testMode
 */
async function handleChatRequestWithSemanticValidation(req, res, params) {
  console.log('🚀 SEMANTIC-FIRST FLOW: Function called with params:', {
    query: params.query,
    apikey: params.apikey ? params.apikey.substring(0, 20) + '...' : 'none'
  });

  try {
    // Step 1: API Key Validation (to get appId and documents)
    console.log('🔍 SEMANTIC-FIRST FLOW: Starting API validation to get context');

    // VALIDATE APIKEY WITH USER-SERVICE ON ABSTRAXN
    const userService = require('../services/userService');
    const apiValidationResult = await userService.validateApiKey(params.apikey, req.get('Origin'));

    // userService.validateApiKey returns the result directly, not wrapped in success object
    if (!apiValidationResult || !apiValidationResult.id) {
      return res.status(401).json({
        error: true,
        message: 'Invalid API key'
      });
    }

    // Step 2: Set validation data for semantic context
    req.validationData = { result: apiValidationResult };
    const appId = apiValidationResult.appId;
    const documents = apiValidationResult.documents || [];

    console.log(`✅ API validation complete. AppId: ${appId}, Documents: ${documents.length}`);

    // Step 3: Enhanced semantic-aware guardrails validation
    const content = params.query;
    if (content) {
      console.log('🛡️ SEMANTIC-FIRST FLOW: Running enhanced guardrails validation');

      const guardrailsValidation = require('../middleware/guardrailsValidation');
      const validationResult = await guardrailsValidation.validateContentWithSemantics(content, appId, documents);

      // Handle validation results
      if (!validationResult.is_valid) {
        console.warn('🚫 Content rejected by semantic-aware guardrails:', {
          content: content.substring(0, 100) + '...',
          errors: validationResult.validation_errors,
          semantic_score: validationResult.semantic_score,
          relevance_level: validationResult.relevance_level
        });

        req.guardrailsRejection = {
          reason: 'Content validation failed',
          validation_errors: validationResult.validation_errors,
          validated_content: validationResult.validated_content,
          semantic_score: validationResult.semantic_score,
          relevance_level: validationResult.relevance_level,
          threat_level: validationResult.threat_level,
          confidence_score: validationResult.confidence_score,
          sanitization_applied: validationResult.sanitization_applied
        };
      }

      // Handle semantic bypass
      if (validationResult.semantic_bypass) {
        console.log('✅ SEMANTIC BYPASS: Query approved without traditional guardrails check');
        req.semanticBypass = true;
      }

      // Update content if sanitized
      if (validationResult.validated_content !== content) {
        params.query = validationResult.validated_content;
        console.log('🧹 Content sanitized by semantic-aware guardrails:', {
          original: content.substring(0, 50) + '...',
          sanitized: validationResult.validated_content.substring(0, 50) + '...',
          semantic_score: validationResult.semantic_score
        });
      }

      // Store validation context for system prompt enhancement
      if (validationResult.validation_errors && validationResult.validation_errors.length > 0) {
        req.guardrailsValidation = {
          validation_errors: validationResult.validation_errors,
          sanitized_content: validationResult.validated_content,
          semantic_score: validationResult.semantic_score,
          relevance_level: validationResult.relevance_level
        };
      }
    }

    // Step 4: Continue with normal chat processing (skip API validation since we already did it)
    console.log('🚀 SEMANTIC-FIRST FLOW: Proceeding to normal chat processing');

    // Mark that we've already done API validation to skip it in handleChatRequest
    req.semanticValidationComplete = true;
    req.validationData = { result: apiValidationResult };

    return await handleChatRequest(req, res, params);

  } catch (error) {
    console.error('❌ Semantic-first validation flow error:', error.message);
    // Fallback to normal processing
    return await handleChatRequest(req, res, params);
  }
}

/**
 * Shared function to handle chat requests (used by both GET and POST endpoints)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Object} params - Parameters object containing apikey, query, sessionId, stream, testMode
 */
async function handleChatRequest(req, res, params) {
  let { apikey, query, sessionId, stream = 'true', testMode = 'false' } = params;

  // Test mode for vector database integration testing
  const isTestMode = testMode === 'true';
  if (isTestMode) {
    console.log(`\n🧪 ═══════════════════════════════════════════════════════════════`);
    console.log(`🧪 TEST MODE ACTIVATED - BYPASSING USER SERVICE`);
    console.log(`🧪 Query: "${query}"`);
    console.log(`🧪 ═══════════════════════════════════════════════════════════════\n`);
  }

  // Validate required parameters
  if (!apikey) {
    return res.status(400).json({
      error: true,
      message: 'API key is required'
    });
  }

  if (!query) {
    return res.status(400).json({
      error: true,
      message: 'Query is required'
    });
  }

  // Determine the best query for vector search (use validated content if available, otherwise original)
  let vectorSearchQuery = query; // Default to original query

  // Handle validation from guardrails middleware
  if (req.guardrailsValidation) {
    const validation = req.guardrailsValidation;
    const validatedContent = validation.sanitized_content;

    // Log guardrails decision for debugging
    console.log(`🛡️ Guardrails Decision: ${validation.validation_action || 'UNKNOWN'}`, {
      threat_level: validation.threat_level,
      confidence_score: validation.confidence_score,
      sanitization_applied: validation.sanitization_applied,
      reason: validation.validation_reason
    });
  }

  // Handle validation from semantic-first flow
  if (req.guardrailsRejection) {
    const rejection = req.guardrailsRejection;

    // Log guardrails decision for debugging
    console.log(`🛡️ Guardrails Decision: REJECT`, {
      threat_level: rejection.threat_level,
      confidence_score: rejection.confidence_score,
      sanitization_applied: rejection.sanitization_applied,
      reason: rejection.reason
    });

    // Use validated/sanitized content for vector search if it's different (means it was sanitized)
    if (rejection.validated_content && rejection.validated_content !== query) {
      vectorSearchQuery = rejection.validated_content;
      console.log(`🔍 Using sanitized query for vector search: "${vectorSearchQuery}"`);
      console.log(`📝 Original user query was: "${query}"`);
    }

    // For rejections, we don't proceed with warnings or sanitization - they are blocked
    // This logic is handled in the guardrailsRejection check later in the code
  }

  const streamBool = stream === 'true' || stream === true;

  // Log semantic validation results
  if (req.semanticBypass) {
    console.log('✅ SEMANTIC BYPASS: Request approved without guardrails validation');
  } else if (req.guardrailsValidation) {
    console.log('🛡️ ENHANCED GUARDRAILS: Request processed with semantic context:', {
      semantic_score: req.guardrailsValidation.semantic_score,
      relevance_level: req.guardrailsValidation.relevance_level,
      validation_errors: req.guardrailsValidation.validation_errors?.length || 0
    });
  }

  // Check if guardrails rejected the request
  if (req.guardrailsRejection) {
    console.log('🚫 Request rejected by enhanced guardrails - returning consistent error format:', {
      semantic_score: req.guardrailsRejection.semantic_score,
      relevance_level: req.guardrailsRejection.relevance_level,
      validation_errors: req.guardrailsRejection.validation_errors,
      validation_errors_count: req.guardrailsRejection.validation_errors?.length || 0,
      validated_content: req.guardrailsRejection.validated_content,
      validated_content_length: req.guardrailsRejection.validated_content?.length || 0,
      threat_level: req.guardrailsRejection.threat_level,
      confidence_score: req.guardrailsRejection.confidence_score,
      sanitization_applied: req.guardrailsRejection.sanitization_applied
    });

    // Generate a session ID for consistency
    const rejectionSessionId = require('crypto').randomUUID();

    // Create enhanced error response format
    let responseMessage;

    // Check if this is an out-of-domain rejection (has relevance_level 'out-of-domain')
    if (req.guardrailsRejection.relevance_level === 'out-of-domain') {
      // For out-of-domain: Use our enhanced out-of-domain responses
      if (req.guardrailsRejection.validation_errors && req.guardrailsRejection.validation_errors.length > 0) {
        responseMessage = req.guardrailsRejection.validation_errors[0];

        // Add suggestions if available (remaining errors are suggestions)
        if (req.guardrailsRejection.validation_errors.length > 1) {
          const suggestions = req.guardrailsRejection.validation_errors.slice(1, 3); // Limit to 2 suggestions
          responseMessage += '\n\n' + suggestions.join('\n');
        }
      } else {
        responseMessage = "Your question doesn't seem to be related to my area of expertise.";
      }
    } else {
      // For security/safety rejections: Use validated_content if available, otherwise validation_errors
      console.log('🔍 SECURITY REJECTION DEBUG:', {
        has_validated_content: !!req.guardrailsRejection.validated_content,
        validated_content_value: req.guardrailsRejection.validated_content,
        validated_content_length: req.guardrailsRejection.validated_content?.length || 0,
        has_validation_errors: !!(req.guardrailsRejection.validation_errors && req.guardrailsRejection.validation_errors.length > 0),
        validation_errors: req.guardrailsRejection.validation_errors
      });

      // Check if this is a high-threat security issue
      const isHighThreat = req.guardrailsRejection.threat_level === 'high' ||
        (req.guardrailsRejection.confidence_score && req.guardrailsRejection.confidence_score >= 0.9);

      if (isHighThreat) {
        // For high-threat security issues, use validation errors (security rejection message)
        console.log('🚫 High-threat security issue detected - using security rejection message');
        if (req.guardrailsRejection.validation_errors && req.guardrailsRejection.validation_errors.length > 0) {
          responseMessage = `I cannot help with requests that attempt to extract sensitive information. ${req.guardrailsRejection.validation_errors[0]}`;
        } else {
          responseMessage = `I cannot process this request as it violates our security guidelines. Please rephrase your question in a different way.`;
        }
      } else if (req.guardrailsRejection.validated_content && req.guardrailsRejection.validated_content.trim() !== '') {
        // For lower-threat issues, use validated_content (sanitized version)
        console.log('✅ Using validated_content for response (low-threat sanitization)');
        responseMessage = req.guardrailsRejection.validated_content;
      } else if (req.guardrailsRejection.validation_errors && req.guardrailsRejection.validation_errors.length > 0) {
        console.log('⚠️ Using validation_errors for response (no validated_content available)');
        responseMessage = req.guardrailsRejection.validation_errors[0];
      } else {
        console.log('❌ Using fallback generic message');
        responseMessage = `I cannot process this request as it violates our safety guidelines. ${req.guardrailsRejection.reason || ''}`;
      }
    }

    const errorResponse = {
      error: false, // Keep false to maintain API consistency
      sessionId: rejectionSessionId,
      response: responseMessage,
      metadata: {
        blocked: true,
        relevance_level: req.guardrailsRejection.relevance_level,
        semantic_score: req.guardrailsRejection.semantic_score
      }
    };

    if (streamBool) {
      // Handle streaming response for rejected content
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      res.write(`data: ${JSON.stringify({
        type: 'session',
        sessionId: rejectionSessionId,
        timestamp: new Date().toISOString()
      })}\n\n`);

      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: errorResponse.response
      })}\n\n`);

      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString(),
        guardrails: errorResponse.guardrails
      })}\n\n`);

      return res.end();
    } else {
      return res.json(errorResponse);
    }
  }

  try {
    const userService = require('../services/userService');
    // Extract clean origin domain (remove path and query parameters)
    let origin = req.headers.origin || req.headers.referer || 'unknown';
    if (origin !== 'unknown') {
      try {
        const url = new URL(origin);
        origin = `${url.protocol}//${url.host}`;
      } catch (error) {
        console.log(`⚠️ Failed to parse origin URL: ${origin}, using as-is`);
      }
    }

    // Start timing
    const requestStartTime = Date.now();
    const timingLog = {
      requestStart: requestStartTime,
      steps: []
    };

    console.log(`⏱️ [TIMING] Request started at: ${new Date().toISOString()}`);
    console.log(`⏱️ [TIMING] Query: "${query.substring(0, 100)}${query.length > 100 ? '...' : ''}"`);

    // Step 1: PARALLEL PROCESSING OPTIMIZATION - API Key Validation & Setup
    console.log(`\n⚡ ═══════════════════════════════════════════════════════════════`);
    console.log(`⚡ PARALLEL PROCESSING OPTIMIZATION ENABLED`);
    console.log(`⚡ Running API validation and service setup in parallel`);
    console.log(`⚡ ═══════════════════════════════════════════════════════════════\n`);

    const cacheService = require('../services/hybridCacheService');

    let validationResult;
    let userServiceDuration = 0;
    let appId, chatAiId;
    let chatAiData; // Declare chatAiData in broader scope

    // Start API key validation (async)
    const validationPromise = (async () => {

      console.log('🔑 Checking API key validation cache...');

      let cachedResult = await cacheService.getCachedApiKeyValidation(apikey);
      console.log(cachedResult, "cachedResult");
      if (!cachedResult) {
        // Cache miss - call User Service key-validator (which now includes documents!)
        console.log(`🔄 USER SERVICE CALL - CACHE MISS - API KEY VALIDATION`);

        const userServiceStartTime = Date.now();
        const result = await userService.validateApiKey(apikey, origin);
        const duration = Date.now() - userServiceStartTime;

        console.log(`✅ USER SERVICE SUCCESS: API key validation completed in ${duration}ms`);

        // Cache the validation result
        await cacheService.cacheApiKeyValidation(apikey, { result });
        return { result, duration };
      } else {
        console.log(`⚡ Using cached API key validation (saved ~200-500ms)`);
        return { result: cachedResult.result, duration: 0 };
      }

    })();

    // Start service initialization (parallel with validation)
    const serviceSetupPromise = (async () => {
      console.log(`🔧 Pre-initializing services in parallel...`);
      // Pre-load services that don't need validation data
      const vectorSearchService = require('../services/vectorSearchService');

      // Pre-warm any caches or connections if needed
      console.log(`✅ Services pre-initialized successfully`);
      return { vectorSearchService, openRouterService };
    })();

    // Wait for both validation and service setup to complete
    const [validationData, services] = await Promise.all([
      validationPromise,
      serviceSetupPromise
    ]);
    console.log(validationData, "validationData")
    // Extract validation results
    validationResult = validationData.result;
    userServiceDuration = validationData.duration;
    chatAiData = validationResult;
    appId = chatAiData.appId;
    chatAiId = chatAiData.id;
    systemPrompt = chatAiData.systemPrompt || '';

    if (!appId || !chatAiId) {
      throw new Error('Invalid API key: missing appId or chatAiId');
    }

    // Log validation timing
    const validationEndTime = Date.now();
    const validationTotalTime = validationEndTime - requestStartTime;
    timingLog.steps.push({
      step: 'api_validation',
      duration: userServiceDuration,
      totalElapsed: validationTotalTime,
      timestamp: new Date().toISOString()
    });
    console.log('systemPrompt', systemPrompt.substring(0, 100) + '...');
    console.log(`✅ API key validated successfully. AppId: ${appId}, ChatAiId: ${chatAiId}`);
    console.log(`⚡ Parallel processing completed - validation and setup done simultaneously`);
    console.log(`⏱️ [TIMING] API Validation: ${userServiceDuration}ms (Total: ${validationTotalTime}ms)`);

    // Step 2: Credit Deduction (BEFORE expensive processing)
    const creditStartTime = Date.now();
    if (!isTestMode) {
      console.log(`\n💰 ═══════════════════════════════════════════════════════════════`);
      console.log(`💰 STEP 2: CREDIT DEDUCTION FOR QUERY`);
      console.log(`💰 ChatAI ID: ${chatAiId}`);
      console.log(`💰 Query: "${query}"`);
      console.log(`💰 ═══════════════════════════════════════════════════════════════\n`);

      const databaseService = require('../services/databaseService');

      // Get userId from database using chatAiId (since API key validation doesn't return userId)
      let userId;
      try {
        const dataSource = databaseService.getDataSource();
        const chatAiRepo = dataSource.getRepository('ChatAi');
        const chatAiRecord = await chatAiRepo.findOne({
          where: { id: chatAiId },
          select: ['userId'],
        });

        if (!chatAiRecord) {
          console.log(`❌ ChatAI project not found: ${chatAiId}`);
          return res.status(404).json({
            error: true,
            message: 'ChatAI project not found',
          });
        }

        userId = chatAiRecord.userId;
        console.log(`👤 User ID retrieved: ${userId}`);

      } catch (error) {
        console.error(`❌ Failed to get user ID: ${error.message}`);
        return res.status(500).json({
          error: true,
          message: 'Failed to validate user permissions',
        });
      }

      const creditResult = await databaseService.deductQueryCredits(
        chatAiId,
        userId,
        1, // 1 credit per query
        {
          queryType: 'chat_query',
          sessionId: sessionId,
          query: query,
          timestamp: new Date().toISOString(),
        }
      );

      if (!creditResult.success) {
        console.log(`❌ Credit deduction failed: ${creditResult.message}`);

        return res.status(403).json({
          error: true,
          message: creditResult.message,
          creditsRemaining: creditResult.creditsRemaining,
          subscriptionStatus: creditResult.subscriptionStatus,
          upgradeMessage: creditResult.subscriptionStatus === 'free'
            ? 'Upgrade to Pro for unlimited queries!'
            : undefined,
        });
      }

      console.log(`✅ Credits deducted successfully!`);
      console.log(`💰 Remaining credits: ${creditResult.creditsRemaining}`);
      console.log(`👑 Subscription: ${creditResult.subscriptionStatus}${creditResult.isPremium ? ' (Premium)' : ''}`);
    } else {
      console.log(`🧪 Test mode: Skipping credit deduction`);
    }

    // Log credit deduction timing
    const creditEndTime = Date.now();
    const creditDuration = creditEndTime - creditStartTime;
    const creditTotalTime = creditEndTime - requestStartTime;
    timingLog.steps.push({
      step: 'credit_deduction',
      duration: creditDuration,
      totalElapsed: creditTotalTime,
      timestamp: new Date().toISOString(),
      skipped: isTestMode
    });
    console.log(`⏱️ [TIMING] Credit Deduction: ${creditDuration}ms (Total: ${creditTotalTime}ms)`);

    // Step 3: Services will be imported in parallel processing section

    // Step 4: Get or create session using appId
    const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

    // Step 5: Use documents from key-validator response (or mock data in test mode)
    let documents;


    documents = chatAiData.documents || [];
    console.log(`📄 Using documents from key-validator response: ${documents.length} documents available`);
    // console.log(`🔍 DEBUG: chatAiData.documents = ${JSON.stringify(chatAiData.documents, null, 2)}`);
    console.log(`⚡ OPTIMIZATION: Saved 1 API call by including documents in key-validator response`);

    // DYNAMIC SYSTEM PROMPT: Generate based on document type detection
    const documentTypeDetector = require('../services/documentTypeDetector');
    const detectionResult = documentTypeDetector.detectDocumentType(documents, query);

    // Override hardcoded system prompt with dynamic one
    const dynamicSystemPrompt = documentTypeDetector.generateSystemPrompt(detectionResult);
    systemPrompt = dynamicSystemPrompt;

    // console.log(`🔄 Dynamic system prompt generated:`, {
    //   domain: detectionResult.domain,
    //   role: detectionResult.role,
    //   confidence: detectionResult.confidence,
    //   originalPrompt: chatAiData.systemPrompt?.substring(0, 50) + '...',
    //   newPrompt: systemPrompt.substring(0, 100) + '...'
    // });

    // Cache the documents for future requests in this session
    await cacheService.cacheDocuments(currentSessionId, appId, documents, null);

    // Step 5 & 6: VECTOR SEARCH PREPARATION (Semantic Refinement DISABLED for performance)
    console.log(`\n⚡ ═══════════════════════════════════════════════════════════════`);
    console.log(`⚡ STEP 5-6: VECTOR SEARCH PREPARATION (SEMANTIC REFINEMENT DISABLED)`);
    console.log(`⚡ Using original query directly for maximum performance`);
    console.log(`⚡ ═══════════════════════════════════════════════════════════════\n`);

    // COMMENTED OUT: Semantic refinement (saves ~1480ms)
    // const semanticRefinementPromise = (async () => {
    //   console.log(`🧠 Starting semantic query refinement...`);
    //   console.log(`🧠 Original query: "${query}"`);
    //
    //   const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);
    //   const contextMetadata = {
    //     appId,
    //     documentCount: documents.length,
    //     documentTypes: documents.map(d => d.type || 'unknown'),
    //     domain: chatAiData?.domain || 'general'
    //   };
    //
    //   const refinementResult = await promptPreprocessor.refineQuery(
    //     query,
    //     conversationHistory,
    //     contextMetadata,
    //     currentSessionId
    //   );
    //
    //   const preprocessDuration = Date.now() - preprocessStartTime;
    //   return { refinementResult, preprocessDuration };
    // })();

    // Create mock refinement result to maintain compatibility
    const semanticRefinementPromise = (async () => {
      console.log(`🚀 PERFORMANCE MODE: Skipping semantic refinement (saves ~1480ms)`);
      console.log(`📝 Using vector search query: "${vectorSearchQuery}"`);

      const mockRefinementResult = {
        original: vectorSearchQuery,
        refined: vectorSearchQuery, // Use clean/sanitized query for vector search
        refinements: [],
        cached: false,
        processingTime: 0
      };

      const preprocessDuration = 0; // No processing time
      console.log(`⚡ Mock semantic refinement completed in ${preprocessDuration}ms`);

      return { refinementResult: mockRefinementResult, preprocessDuration };
    })();

    // Start vector search preparation (parallel with semantic refinement)
    const vectorSearchPrepPromise = (async () => {
      console.log(`🔍 Preparing vector search components...`);

      // Pre-initialize vector search service
      const vectorSearchService = require('../services/vectorSearchService');

      // Pre-check cache with vector search query (fallback)
      const originalQueryCache = await cacheService.getCachedContext(currentSessionId, vectorSearchQuery);

      console.log(`🔍 Vector search preparation completed`);
      console.log(`🔍 Documents available: ${documents.length}`);
      console.log(`🔍 AppId: ${appId}`);

      return { vectorSearchService, originalQueryCache };
    })();

    // Wait for both semantic refinement and vector search prep
    console.log(`⏳ Waiting for semantic refinement and vector search prep...`);
    const [semanticData, vectorPrepData] = await Promise.all([
      semanticRefinementPromise,
      vectorSearchPrepPromise
    ]);

    // Extract results
    const { refinementResult, preprocessDuration } = semanticData;
    const { vectorSearchService, originalQueryCache } = vectorPrepData;
    const refinedQuery = refinementResult.refined;

    // Log semantic refinement timing (disabled for performance)
    const semanticEndTime = Date.now();
    const semanticTotalTime = semanticEndTime - requestStartTime;
    timingLog.steps.push({
      step: 'semantic_refinement',
      duration: preprocessDuration, // Will be 0
      totalElapsed: semanticTotalTime,
      timestamp: new Date().toISOString(),
      refinementsApplied: 0, // No refinements applied
      originalQuery: vectorSearchQuery,
      refinedQuery: refinedQuery, // Same as original
      disabled: true // Flag to indicate it was disabled
    });

    console.log(`⚡ Vector search preparation completed (semantic refinement disabled)!`);
    console.log(`⏱️ [TIMING] Semantic Refinement: ${preprocessDuration}ms (DISABLED - Total: ${semanticTotalTime}ms)`);

    // Step 6: Vector Database Context Retrieval (now with refined query)
    const vectorSearchStartTime = Date.now();
    let finalContext = '';
    let vectorSearchDuration = 0;

    console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
    console.log(`🔍 STEP 6: VECTOR DATABASE CONTEXT RETRIEVAL`);
    console.log(`🔍 Using refined query: "${refinedQuery}"`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    if (documents.length > 0) {
      // Check cache using refined query for better cache hits
      const cachedContext = await cacheService.getCachedContext(currentSessionId, refinedQuery);
      console.log(cachedContext, "cachedContext")
      if (cachedContext) {
        finalContext = cachedContext.context;
        console.log(`⚡ Using cached context for refined query (saved ~1-3s)`);
        console.log(`📝 Cached context length: ${finalContext.length} characters`);

        // Log cached vector search timing
        const vectorEndTime = Date.now();
        const vectorTotalTime = vectorEndTime - requestStartTime;
        timingLog.steps.push({
          step: 'vector_search',
          duration: 0,
          totalElapsed: vectorTotalTime,
          timestamp: new Date().toISOString(),
          documentsSearched: documents.length,
          contextLength: finalContext.length,
          cached: true
        });
        console.log(`⏱️ [TIMING] Vector Search: 0ms (cached) (Total: ${vectorTotalTime}ms)`);
      } else if (originalQueryCache && originalQueryCache.context && refinedQuery === vectorSearchQuery) {
        // Use original query cache if no refinement was applied
        finalContext = originalQueryCache.context;
        console.log(originalQueryCache, "originalQueryCache")
        console.log(`⚡ Using cached context for original query (saved ~1-3s)`);
        console.log(`📝 Cached context length: ${finalContext.length} characters`);

        // Log cached vector search timing
        const vectorEndTime = Date.now();
        const vectorTotalTime = vectorEndTime - requestStartTime;
        timingLog.steps.push({
          step: 'vector_search',
          duration: 0,
          totalElapsed: vectorTotalTime,
          timestamp: new Date().toISOString(),
          documentsSearched: documents.length,
          contextLength: finalContext.length,
          cached: true
        });
        console.log(`⏱️ [TIMING] Vector Search: 0ms (cached) (Total: ${vectorTotalTime}ms)`);
      } else {
        console.log(`🔍 Retrieving context from Qdrant Vector Database for ${documents.length} documents...`);
        console.log(`📄 Document details:`);
        documents.forEach((doc, index) => {
          console.log(`   ${index + 1}. ${doc.filename || 'Unknown'} (ID: ${doc.id})`);
        });

        // Use refined query for better vector search results
        finalContext = await vectorSearchService.retrieveFromMultipleDocuments(documents, refinedQuery, appId);
        vectorSearchDuration = Date.now() - vectorSearchStartTime;

        // Cache the context using refined query
        await cacheService.cacheContext(currentSessionId, refinedQuery, finalContext);
        console.log(`✅ Context retrieved from vector database and cached in ${vectorSearchDuration}ms`);
        console.log(`📝 Final context length: ${finalContext.length} characters`);

        // Log vector search timing
        const vectorEndTime = Date.now();
        const vectorTotalTime = vectorEndTime - requestStartTime;
        timingLog.steps.push({
          step: 'vector_search',
          duration: vectorSearchDuration,
          totalElapsed: vectorTotalTime,
          timestamp: new Date().toISOString(),
          documentsSearched: documents.length,
          contextLength: finalContext.length,
          cached: false
        });
        console.log(`⏱️ [TIMING] Vector Search: ${vectorSearchDuration}ms (Total: ${vectorTotalTime}ms)`);
      }
    } else {
      console.log(`⚠️ No documents available for context retrieval - returning static message`);

      // Return static message when no documents are available
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      await cacheService.addConversationEntry(currentSessionId, query, staticResponse, {
        documentsUsed: 0,
        contextLength: 0,
        cached: {
          apiKey: userServiceDuration === 0,
          context: false
        },
        staticResponse: true,
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration
        }
      });

      console.log(`⏱️ [TIMING] Static response completed in: ${totalDuration}ms`);

      // Log static response
      queryLogger.logQueryResponse(query, staticResponse, null);

      return res.json({
        error: false,
        sessionId: currentSessionId,
        response: staticResponse
      });
    }

    // Step 7: Generate response
    console.log('\n🔍 ═══════════════════════════════════════════════════════════════');
    console.log('🔍 FINAL CONTEXT BEING SENT TO OPENROUTER');
    console.log('🔍 ═══════════════════════════════════════════════════════════════');
    console.log(`📝 Original Query: "${query}"`);
    console.log(`🧠 Refined Query: "${refinedQuery}"`);
    console.log(`📄 Final Context Length: ${finalContext.length} characters`);
    console.log(`📊 Documents Used: ${documents.length}`);
    console.log(`🧠 Refinements Applied: ${refinementResult.refinements.length}`);
    // console.log('\n📋 FINAL CONTEXT CONTENT:');
    // console.log('─'.repeat(80));
    // console.log(finalContext);
    // console.log('─'.repeat(80));
    console.log('🔍 ═══════════════════════════════════════════════════════════════\n');

    // Check if context is empty or insufficient (out-of-knowledge-base query)
    if (!finalContext || (typeof finalContext === 'string' && finalContext.trim().length === 0) || (typeof finalContext !== 'string' && !finalContext)) {
      console.log(`⚠️ No relevant context found for query - this appears to be outside the knowledge base`);

      // Generate dynamic response based on available documents
      const outOfContextResponse = generateDynamicOutOfContextResponse(documents);
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      await cacheService.addConversationEntry(currentSessionId, query, outOfContextResponse, {
        documentsUsed: documents.length,
        contextLength: 0,
        outOfContext: true,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        },
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration
        }
      });

      console.log(`⏱️ [TIMING] Out-of-context response completed in: ${totalDuration}ms`);

      if (streamBool) {
        // Handle streaming response for out-of-context
        res.write(`data: ${JSON.stringify({
          type: 'session',
          sessionId: currentSessionId,
          timestamp: new Date().toISOString()
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'content',
          content: outOfContextResponse
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'done',
          timestamp: new Date().toISOString(),
          timing: { total: totalDuration },
          outOfContext: true
        })}\n\n`);

        res.end();
        return;
      } else {
        return res.json({
          error: false,
          sessionId: currentSessionId,
          response: outOfContextResponse,
          outOfContext: true
        });
      }
    }

    // Context is available - proceed with normal response generation
    if (streamBool) {
      const refinementData = {
        applied: refinementResult.refinements.length > 0,
        refinements: refinementResult.refinements.length,
        processingTime: preprocessDuration,
        originalQuery: query,
        refinedQuery: refinedQuery
      };
      try {
        // Prepare options for OpenRouter service
        const openRouterOptions = {
          model: params.model,
          temperature: params.temperature,
          maxTokens: params.maxTokens,
          context: params.context,
          metadata: params.metadata,
          systemPrompt: systemPrompt,
          validationInfo: req.guardrailsValidation
        };

        // Use sanitized query if available, otherwise use original query (validation context now in system prompt)
        const finalQuery = req.guardrailsValidation?.sanitized_content || query;
        await streamChatResponse(res, finalQuery, finalContext, currentSessionId, requestStartTime, cacheService, documents.length, refinementData, timingLog, openRouterOptions);
      } catch (streamError) {
        // streamChatResponse already handled the error and sent response, just log it
        console.error('❌ Stream response error already handled:', streamError.message);
      }
    } else {
      // Get conversation history for context
      const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);

      const openRouterStartTime = Date.now();

      // Prepare refinement info for OpenRouter logging
      const refinementInfo = {
        originalQuery: query,
        refinedQuery: refinedQuery,
        refinementsCount: refinementResult.refinements.length,
        processingTime: preprocessDuration,
        refinements: refinementResult.refinements
      };

      // Log OpenRouter API call start for non-streaming
      const openRouterCallTotalTime = openRouterStartTime - requestStartTime;
      timingLog.steps.push({
        step: 'openrouter_call_start',
        duration: 0,
        totalElapsed: openRouterCallTotalTime,
        timestamp: new Date().toISOString()
      });
      console.log(`⏱️ [TIMING] OpenRouter API Call Started (Total: ${openRouterCallTotalTime}ms)`);

      // Prepare options for OpenRouter service
      const openRouterOptions = {
        model: params.model,
        temperature: params.temperature,
        maxTokens: params.maxTokens,
        context: params.context,
        systemPrompt: systemPrompt,
        validationInfo: req.guardrailsValidation
      };

      // Use sanitized query if available, otherwise use original query (validation context now in system prompt)
      const finalQuery = req.guardrailsValidation?.sanitized_content || query;
      const response = await openRouterService.generateResponse(finalQuery, finalContext, conversationHistory, refinementInfo, openRouterOptions);
      const openRouterDuration = Date.now() - openRouterStartTime;

      // Log OpenRouter completion timing for non-streaming
      const totalDuration = Date.now() - requestStartTime;
      timingLog.steps.push({
        step: 'openrouter_complete',
        duration: openRouterDuration,
        totalElapsed: totalDuration,
        timestamp: new Date().toISOString(),
        responseLength: response.length,
        streaming: false
      });
      console.log(`⏱️ [TIMING] OpenRouter Complete: ${openRouterDuration}ms (Total: ${totalDuration}ms)`);

      // Log final timing summary for non-streaming
      console.log(`\n⏱️ [TIMING SUMMARY] Complete request flow:`);
      timingLog.steps.forEach((step, index) => {
        console.log(`   ${index + 1}. ${step.step}: ${step.duration}ms (Total: ${step.totalElapsed}ms)`);
      });
      console.log(`⏱️ [TIMING SUMMARY] Total Duration: ${totalDuration}ms\n`);

      // Add to conversation history
      await cacheService.addConversationEntry(currentSessionId, query, response, {
        documentsUsed: documents.length,
        contextLength: finalContext.length,
        conversationHistoryLength: conversationHistory.length,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        },
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration,
          originalQuery: query,
          refinedQuery: refinedQuery
        },
        metadata: params.metadata || null
      });

      console.log(`⏱️ [TIMING] Total request completed in: ${totalDuration}ms`);
      console.log(`📤 RESPONSE BEING SENT TO CLIENT`);
      console.log(`📤 ═══════════════════════════════════════════════════════════════`);
      console.log(`📄 Response Type: JSON (Non-streaming)`);
      console.log(`🔢 Session ID: ${currentSessionId}`);
      console.log(`📏 Response Length: ${response.length} characters`);
      console.log(`⏱️ Total Duration: ${totalDuration}ms`);
      console.log(`✅ Status: Success`);
      console.log(`📋 Response Preview: "${response.substring(0, 100)}${response.length > 100 ? '...' : ''}"`);
      console.log(`📤 ═══════════════════════════════════════════════════════════════\n`);

      // Log successful non-streaming response
      queryLogger.logQueryResponse(query, response, null);

      res.json({
        error: false,
        sessionId: currentSessionId,
        response
      });
    }

  } catch (error) {
    console.error('❌ API v1 chat error:', error.message);

    // Log error
    queryLogger.logQueryResponse(query, null, error.message);

    // Return error in JSON format if headers not sent yet
    if (!res.headersSent) {
      console.log('📤 Sending error response from main handler');
      const statusCode = error.message.includes('validation failed') ? 403 :
        error.message.includes('not found') ? 404 : 500;

      return res.status(statusCode).json({
        error: true,
        message: error.message
      });
    } else {
      console.log('⚠️ Headers already sent, skipping error response from main handler');
    }
  }
}

// Enhanced semantic-first validation endpoint
router.post('/api/v1/chat/', async (req, res) => {
  // Extract parameters from request body for POST
  const params = {
    apikey: req.body.apikey || req.headers.authorization?.replace('Bearer ', ''),
    query: req.body.query,
    sessionId: req.body.sessionId,
    stream: req.body.stream !== undefined ? req.body.stream.toString() : 'true',
    testMode: req.body.testMode !== undefined ? req.body.testMode.toString() : 'false',
    // Additional POST-specific parameters
    model: req.body.model,
    temperature: req.body.temperature,
    maxTokens: req.body.maxTokens,
    context: req.body.context,
    metadata: req.body.metadata
  };

  await handleChatRequestWithSemanticValidation(req, res, params);
});


// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const vectorSearchService = require('../services/vectorSearchService');

    // Check vector database status
    let vectorDbStatus = 'unknown';
    let vectorDbStats = {};

    try {
      vectorDbStats = await vectorSearchService.getStats();
      vectorDbStatus = vectorDbStats.status === 'green' ? 'healthy' : 'degraded';
    } catch (error) {
      vectorDbStatus = 'error';
      vectorDbStats = { error: error.message };
    }

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ChatAI',
      endpoints: {
        main: 'POST /api/v1/chat/',
        health: '/health',
        apiHealth: '/api/v1/chat/health'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean',
      error: error.message
    });
  }
});

// API v1 Health check endpoint
router.get('/api/v1/chat/health', async (req, res) => {
  try {
    const vectorSearchService = require('../services/vectorSearchService');

    // Check vector database status
    let vectorDbStatus = 'unknown';
    let vectorDbStats = {};

    try {
      vectorDbStats = await vectorSearchService.getStats();
      vectorDbStatus = vectorDbStats.status === 'green' ? 'healthy' : 'degraded';
    } catch (error) {
      vectorDbStatus = 'error';
      vectorDbStats = { error: error.message };
    }

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ChatAI API v1',
      version: '1.0',
      endpoints: {
        main: 'POST /api/v1/chat/',
        health: '/health',
        apiHealth: '/api/v1/chat/health'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean API v1',
      version: '1.0',
      error: error.message
    });
  }
});

// AWS Health check endpoint
router.get('/health/aws', async (req, res) => {
  try {
    const configManager = require('../config');
    const { databaseConfigManager } = require('../config/database');

    // Get health status from all AWS-related services
    const [configHealth, databaseHealth] = await Promise.all([
      configManager.getHealthStatus(),
      databaseConfigManager.getHealthStatus()
    ]);

    const overallStatus = configHealth.awsSecretsManager.status === 'healthy' ||
      configHealth.awsSecretsManager.status === 'fallback' ? 'healthy' : 'degraded';

    res.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      service: 'ChatAI AWS Integration',
      aws: {
        secretsManager: configHealth.awsSecretsManager,
        configuration: configHealth.configuration,
        database: databaseHealth
      },
      summary: {
        usingSecretsManager: configHealth.awsSecretsManager.configured,
        credentialsAvailable: configHealth.awsSecretsManager.status !== 'error',
        fallbackMode: configHealth.awsSecretsManager.status === 'fallback'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI AWS Integration',
      error: error.message
    });
  }
});

// Model status endpoint for monitoring fallback models
router.get('/health/models', (req, res) => {
  try {
    const openRouterService = require('../services/openRouterService');
    const modelStatus = openRouterService.getModelStatus();

    res.json({
      status: 'success',
      timestamp: new Date().toISOString(),
      service: 'ChatAI Model Status',
      modelStatus: modelStatus,
      summary: {
        availableModels: modelStatus.availableCount,
        totalModels: modelStatus.totalCount,
        healthStatus: modelStatus.availableCount > 0 ? 'healthy' : 'degraded',
        fallbackEnabled: true
      },
      actions: {
        resetRateLimits: 'POST /health/models/reset'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI Model Status',
      error: error.message
    });
  }
});

// Reset model rate limits endpoint (for testing/manual intervention)
router.post('/health/models/reset', (req, res) => {
  try {
    const openRouterService = require('../services/openRouterService');
    openRouterService.resetAllRateLimits();

    const modelStatus = openRouterService.getModelStatus();

    res.json({
      status: 'success',
      timestamp: new Date().toISOString(),
      service: 'ChatAI Model Status',
      message: 'All model rate limits have been reset',
      modelStatus: modelStatus
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI Model Status',
      error: error.message
    });
  }
});

// Query logs endpoint for monitoring (JSON view)
router.get('/api/v1/logs/queries', async (req, res) => {
  try {
    const lines = parseInt(req.query.lines) || 10;
    const recentLogs = queryLogger.getRecentLogs(lines);
    const stats = queryLogger.getLogStats();

    res.json({
      error: false,
      stats: stats,
      recentLogs: recentLogs,
      message: `Retrieved ${recentLogs.length} recent query logs`
    });
  } catch (error) {
    console.error('❌ Error retrieving query logs:', error.message);
    res.status(500).json({
      error: true,
      message: 'Failed to retrieve query logs'
    });
  }
});

// Query logs download endpoint
router.get('/api/v1/logs/queries/download', async (req, res) => {
  try {
    const stats = queryLogger.getLogStats();

    if (!stats.exists) {
      return res.status(404).json({
        error: true,
        message: 'Log file not found'
      });
    }

    // Set headers for file download
    const filename = `query-logs-${new Date().toISOString().split('T')[0]}.log`;
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', stats.size);

    // Stream the file
    const fs = require('fs');
    const fileStream = fs.createReadStream(stats.filePath);

    fileStream.on('error', (error) => {
      console.error('❌ Error streaming log file:', error.message);
      if (!res.headersSent) {
        res.status(500).json({
          error: true,
          message: 'Failed to download log file'
        });
      }
    });

    fileStream.pipe(res);

  } catch (error) {
    console.error('❌ Error downloading query logs:', error.message);
    res.status(500).json({
      error: true,
      message: 'Failed to download query logs'
    });
  }
});

// Query logs live view endpoint (HTML page)
router.get('/api/v1/logs/queries/view', async (req, res) => {
  try {
    const lines = parseInt(req.query.lines) || 50;
    const recentLogs = queryLogger.getRecentLogs(lines);
    const stats = queryLogger.getLogStats();

    // Generate HTML page for live viewing
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Query Logs - Live View</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #007bff; padding-bottom: 15px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .stat-label { font-weight: bold; color: #666; font-size: 12px; text-transform: uppercase; }
        .stat-value { font-size: 18px; font-weight: bold; color: #333; margin-top: 5px; }
        .controls { margin-bottom: 20px; padding: 15px; background: #e9ecef; border-radius: 6px; }
        .controls button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        .controls button:hover { background: #0056b3; }
        .log-entry { border: 1px solid #ddd; margin-bottom: 10px; border-radius: 6px; overflow: hidden; }
        .log-header { background: #f8f9fa; padding: 10px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
        .log-content { padding: 15px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .query { background: #e3f2fd; padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .response { background: #f1f8e9; padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .error-msg { background: #ffebee; padding: 10px; border-radius: 4px; color: #c62828; }
        .timestamp { color: #666; font-size: 12px; }
        .no-logs { text-align: center; padding: 40px; color: #666; }
        pre { white-space: pre-wrap; word-wrap: break-word; margin: 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Query Logs - Live View</h1>
            <p>Real-time monitoring of user queries and AI responses</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-label">Total Entries</div>
                <div class="stat-value">${stats.entries || 0}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">File Size</div>
                <div class="stat-value">${stats.size ? (stats.size / 1024).toFixed(2) + ' KB' : '0 KB'}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Last Updated</div>
                <div class="stat-value">${stats.lastModified ? new Date(stats.lastModified).toLocaleString() : 'Never'}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Showing</div>
                <div class="stat-value">${recentLogs.length} recent</div>
            </div>
        </div>

        <div class="controls">
            <button onclick="window.location.reload()">🔄 Refresh</button>
            <button onclick="window.location.href='/api/v1/logs/queries/download'">📥 Download</button>
            <button onclick="window.location.href='/api/v1/logs/queries/view?lines=10'">Show 10</button>
            <button onclick="window.location.href='/api/v1/logs/queries/view?lines=50'">Show 50</button>
            <button onclick="window.location.href='/api/v1/logs/queries/view?lines=100'">Show 100</button>
        </div>

        <div class="logs">
            ${recentLogs.length === 0 ?
        '<div class="no-logs">📭 No logs found. Logs will appear here when users make queries.</div>' :
        recentLogs.reverse().map(log => `
                <div class="log-entry ${log.error ? 'error' : 'success'}">
                    <div class="log-header">
                        <span><strong>${log.error ? '❌ Error' : '✅ Success'}</strong></span>
                        <span class="timestamp">${new Date(log.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="log-content">
                        <div class="query">
                            <strong>🔍 Query:</strong>
                            <pre>${log.query || 'N/A'}</pre>
                        </div>
                        ${log.prompt ? `
                        <div class="response">
                            <strong>🤖 Response:</strong>
                            <pre>${log.prompt}</pre>
                        </div>
                        ` : ''}
                        ${log.error ? `
                        <div class="error-msg">
                            <strong>⚠️ Error:</strong>
                            <pre>${log.error}</pre>
                        </div>
                        ` : ''}
                    </div>
                </div>
              `).join('')
      }
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 12px;">
            <p>Auto-refresh this page to see new logs • <a href="/api/v1/logs/queries">JSON API</a> • <a href="/api/v1/logs/queries/download">Download File</a></p>
        </div>
    </div>
</body>
</html>`;

    res.setHeader('Content-Type', 'text/html');
    res.send(html);

  } catch (error) {
    console.error('❌ Error generating log view:', error.message);
    res.status(500).json({
      error: true,
      message: 'Failed to generate log view'
    });
  }
});

// Query logs search endpoint
router.get('/api/v1/logs/queries/search', async (req, res) => {
  try {
    const searchTerm = req.query.q || req.query.search;
    const maxResults = parseInt(req.query.limit) || 50;

    if (!searchTerm) {
      return res.status(400).json({
        error: true,
        message: 'Search term is required. Use ?q=your_search_term'
      });
    }

    const searchResults = queryLogger.searchLogs(searchTerm, maxResults);
    const stats = queryLogger.getLogStats();

    res.json({
      error: false,
      searchTerm: searchTerm,
      totalResults: searchResults.length,
      maxResults: maxResults,
      stats: stats,
      results: searchResults
    });

  } catch (error) {
    console.error('❌ Error searching query logs:', error.message);
    res.status(500).json({
      error: true,
      message: 'Failed to search query logs'
    });
  }
});

// Mount vector processing routes
router.use('/api/vector', vectorProcessingRoutes);

module.exports = router;
