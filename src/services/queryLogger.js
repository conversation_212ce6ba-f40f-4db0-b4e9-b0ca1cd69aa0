const fs = require('fs');
const path = require('path');

class QueryLogger {
  constructor() {
    this.logsDir = path.join(__dirname, '../../logs');
    this.queryLogFile = path.join(this.logsDir, 'query-responses.log');
    this.initializeLogDirectory();
  }

  initializeLogDirectory() {
    try {
      if (!fs.existsSync(this.logsDir)) {
        fs.mkdirSync(this.logsDir, { recursive: true });
        console.log('📁 Created logs directory:', this.logsDir);
      }
    } catch (error) {
      console.error('❌ Failed to create logs directory:', error.message);
    }
  }

  /**
   * Log query and response in the specified format
   * @param {string} query - The user's query
   * @param {string} prompt - The generated response/prompt (null if error)
   * @param {string|null} error - Error message if any (null if successful)
   */
  logQueryResponse(query, prompt = null, error = null) {
    try {
      const logEntry = {
        query: query || '',
        prompt: prompt,
        error: error,
        timestamp: new Date().toISOString()
      };

      // Convert to JSON string and append to file
      const logLine = JSON.stringify(logEntry) + '\n';
      
      fs.appendFileSync(this.queryLogFile, logLine, 'utf8');
      
      // Optional: Log to console for debugging (can be removed in production)
      console.log('📝 Query logged:', { 
        query: query?.substring(0, 50) + (query?.length > 50 ? '...' : ''), 
        hasPrompt: !!prompt, 
        hasError: !!error 
      });
      
    } catch (writeError) {
      console.error('❌ Failed to write query log:', writeError.message);
    }
  }

  /**
   * Read recent query logs (for debugging/monitoring)
   * @param {number} lines - Number of recent lines to read
   * @returns {Array} Array of parsed log entries
   */
  getRecentLogs(lines = 10) {
    try {
      if (!fs.existsSync(this.queryLogFile)) {
        return [];
      }

      const content = fs.readFileSync(this.queryLogFile, 'utf8');
      const logLines = content.trim().split('\n').filter(line => line.trim());
      
      // Get the last N lines
      const recentLines = logLines.slice(-lines);
      
      return recentLines.map(line => {
        try {
          return JSON.parse(line);
        } catch (parseError) {
          console.error('❌ Failed to parse log line:', parseError.message);
          return null;
        }
      }).filter(entry => entry !== null);
      
    } catch (error) {
      console.error('❌ Failed to read query logs:', error.message);
      return [];
    }
  }

  /**
   * Get log file stats
   * @returns {Object} Stats about the log file
   */
  getLogStats() {
    try {
      if (!fs.existsSync(this.queryLogFile)) {
        return { exists: false, size: 0, entries: 0 };
      }

      const stats = fs.statSync(this.queryLogFile);
      const content = fs.readFileSync(this.queryLogFile, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.trim());
      
      return {
        exists: true,
        size: stats.size,
        entries: lines.length,
        lastModified: stats.mtime,
        filePath: this.queryLogFile
      };
      
    } catch (error) {
      console.error('❌ Failed to get log stats:', error.message);
      return { exists: false, size: 0, entries: 0, error: error.message };
    }
  }

  /**
   * Clear the log file (use with caution)
   */
  clearLogs() {
    try {
      if (fs.existsSync(this.queryLogFile)) {
        fs.writeFileSync(this.queryLogFile, '', 'utf8');
        console.log('🗑️ Query logs cleared');
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Failed to clear logs:', error.message);
      return false;
    }
  }
}

// Create singleton instance
const queryLogger = new QueryLogger();

module.exports = queryLogger;
